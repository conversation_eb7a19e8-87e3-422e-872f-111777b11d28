import { db } from "../../utils/model";

export const getCafeteriaSystemAccount = async () => {
  return await db.systemAccount.findFirst({
    where: {
      type: 'CAFETERIA',
    },
    include: {
      account: true,
    },
  });
};

export const getMainSystemAccount = async () => {
  return await db.systemAccount.findFirst({
    where: {
      type: 'COMPANY',
    },
    include: {
      account: true,
    },
  });
};
